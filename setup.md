Download the compose file

curl -LfO 'https://airflow.apache.org/docs/apache-airflow/3.0.1/docker-compose.yaml'

https://airflow.apache.org/docs/apache-airflow/3.0.1/docker-compose.yaml

Setup folder structure

``` 
    mkdir -p ./dags ./logs ./plugins ./config 
    echo -e "AIRFLOW_UID=$(id -u)" > .env	
```

Seed default values to ./config/airflow.cfg

`docker compose run airflow-cli airflow config list`

When you startup airflow, make sure you set:

`load_examples = False`

inside your `airflow.cfg`

If you have already started airflow with this not set to false, you can set it to `False` and run `airflow db reset`

Initialize the database

`docker compose up airflow-init`

Start Airflow

`docker compose up`