import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.integrate import trapezoid

# --- CONFIGURATION ---
excel_file    = r"C:\Users\<USER>\OneDrive - University of Cambridge\Dissertation\Results\Static T1 - 15.05.25\Static3.xlsx"
output_folder = r"C:\Users\<USER>\OneDrive - University of Cambridge\Dissertation\Results\Static T1 - 15.05.25"
pixel_to_um   = 2.2      # micrometers per pixel
dt            = 0.5      # seconds between frames
thresholds    = [0.15, 0.20]  # 15%, 20%
fit_start     = 5
fit_end       = 25

# extract base filename (e.g. "ES067") to prefix output files
excel_prefix = os.path.splitext(os.path.basename(excel_file))[0]

# ensure output folder exists
os.makedirs(output_folder, exist_ok=True)

# --- LOAD & CLEAN ---
df = pd.read_excel(excel_file)
df = df.apply(pd.to_numeric, errors='coerce').dropna()

# distance axis in microns
distance_um = df.iloc[:,0].values * pixel_to_um

# intensity profiles (columns = time slices)
profiles = df.iloc[:,1:].values
n_slices = profiles.shape[1]

# time arrays
time_s    = np.arange(n_slices) * dt
time_min  = time_s / 60.0
sqrt_t    = np.sqrt(time_s)

# --- 1) Intensity vs Distance Plot ---
fig1, ax1 = plt.subplots(figsize=(10,6))
for i, t in enumerate(time_s):
    ax1.plot(distance_um, profiles[:,i], label=f"{t:.1f}s")
ax1.set_title("Intensity vs Distance (μm)")
ax1.set_xlabel("Distance (μm)")
ax1.set_ylabel("Intensity (a.u.)")
ax1.grid(True)
fig1.tight_layout(rect=[0,0,0.8,1])
fig1.savefig(os.path.join(output_folder, f"{excel_prefix}_intensity_vs_distance.png"))

# --- 2) Mass Transport vs Time (minutes) ---
areas = trapezoid(profiles, x=distance_um, axis=0)

fig2, ax2 = plt.subplots(figsize=(8,5))
ax2.plot(time_min, areas, 'o-', color='green')
ax2.set_title("Mass Transport over Time")
ax2.set_xlabel("Time (min)")
ax2.set_ylabel("Area (μm^2)")
ax2.grid(True)
fig2.tight_layout()
fig2.savefig(os.path.join(output_folder, f"{excel_prefix}_mass_transport_vs_time.png"))

# --- DISPLAY PLOTS ---
plt.show()