# --- DIFFUSING FRONT CALCULATION ---
fig2, ax2 = plt.subplots(figsize=(8, 5))
results = []

source_idx = np.argmin(np.abs(distance_um - (100 * pixel_to_um)))
source_intensity = profiles[source_idx, 0]

for pct in thresholds:
    threshold = source_intensity * pct
    front_positions = []

    for i in range(profiles.shape[1]):
        profile = profiles[:, i]
        # Search forward until first point BELOW threshold (more robust)
        valid = np.where(profile <= threshold)[0]
        if len(valid) > 0:
            idx = valid[0]
            front_positions.append(distance_um[idx])
        else:
            front_positions.append(np.nan)

    front_positions = np.array(front_positions)
    ax2.plot(sqrt_time, front_positions, 'o-', label=f'{int(pct * 100)}% threshold')

    # Linear fit (skip NaNs)
    x_fit = sqrt_time[fit_start:fit_end]
    y_fit = front_positions[fit_start:fit_end]
    mask = ~np.isnan(y_fit)

    if np.sum(mask) >= 2:
        slope = np.polyfit(x_fit[mask], y_fit[mask], 1)[0]
        D_um2_s = (slope ** 2) / 2
        D_m2_s = D_um2_s * 1e-12
    else:
        slope = D_um2_s = D_m2_s = np.nan

    results.append({
        "Threshold %": int(pct * 100),
        "Slope": slope,
        "Diffusion Coefficient (µm²/s)": D_um2_s,
        "Diffusion Coefficient (m²/s)": D_m2_s
    })

    print(f"Threshold {int(pct * 100)}% → D = {D_um2_s:.3e} µm²/s")

ax2.set_title("Diffusing Front vs √Time")
ax2.set_xlabel("√time (s^0.5)")
ax2.set_ylabel("Distance (μm)")
ax2.grid(True)
ax2.legend()
fig2.tight_layout()
fig2.savefig(os.path.join(output_folder, "diffusing_front_vs_sqrt_time.png"))