{"timestamp":"2025-05-30T10:52:50.591226","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:53:20.961664","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:53:51.337073","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:54:21.702048","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:54:52.056760","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:55:22.322623","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:55:52.580216","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:56:22.885712","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:56:53.295947","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:57:23.732451","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:57:54.125148","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:58:24.541663","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:58:54.988012","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:59:25.527069","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:59:55.725818","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:00:25.844003","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:00:56.040878","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:01:26.190415","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:01:56.380753","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:02:26.549491","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:02:56.895797","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:03:27.042918","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:03:57.239814","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:04:27.423513","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:04:57.661949","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:05:27.900157","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:05:58.100304","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:06:28.383781","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:06:58.639640","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:07:28.886424","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:07:59.364136","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:08:29.622567","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:08:59.932404","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:09:30.332450","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:10:00.677570","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:10:31.163357","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:11:01.486994","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:11:31.726738","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:12:01.999885","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:12:32.418336","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:13:02.727910","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:13:33.076177","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:14:03.356097","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:14:33.721733","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:15:03.914523","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:15:34.171236","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:16:04.389276","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:16:34.644067","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:17:04.893446","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:17:35.210319","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:18:05.980218","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_local_kubernetes_executor.py","logger":"airflow.models.dagbag.DagBag"}
