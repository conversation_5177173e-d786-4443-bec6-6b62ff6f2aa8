{"timestamp":"2025-05-30T10:52:49.885801","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:53:20.467007","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:53:50.868889","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:54:21.261109","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:54:51.599297","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:55:21.876261","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:55:52.135552","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:56:22.425069","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:56:52.808272","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:57:23.180798","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:57:53.657807","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:58:24.102431","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:58:54.486702","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:59:24.995742","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:59:55.334819","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:00:25.438556","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:00:55.638854","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:01:25.791983","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:01:55.959746","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:02:26.151402","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:02:56.472371","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:03:26.657251","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:03:56.859136","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:04:27.038736","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:04:57.262897","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:05:27.493511","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:05:57.708527","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:06:27.895618","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:06:58.215010","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:07:28.454668","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:07:58.908494","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:08:29.204674","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:08:59.534779","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:09:29.899447","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:10:00.255465","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:10:30.739630","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:11:01.078969","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:11:31.333635","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:12:01.601677","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:12:31.941596","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:13:02.325873","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:13:32.677346","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:14:02.954181","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:14:33.280867","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:15:03.502229","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:15:33.761344","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:16:03.998311","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:16:34.247844","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:17:04.489924","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:17:34.775854","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:18:05.237924","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
