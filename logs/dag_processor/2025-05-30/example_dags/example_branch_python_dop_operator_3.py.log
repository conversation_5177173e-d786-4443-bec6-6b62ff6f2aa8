{"timestamp":"2025-05-30T10:52:50.478298","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:53:20.892733","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:53:51.270967","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:54:21.628089","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:54:52.001244","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:55:22.262509","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:55:52.525855","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:56:22.831293","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:56:53.230336","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:57:23.670291","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:57:54.040144","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:58:24.484665","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:58:54.923680","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:59:25.440436","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:59:55.671217","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:00:25.784336","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:00:55.984321","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:01:26.133255","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:01:56.327578","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:02:26.491163","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:02:56.824323","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:03:26.989770","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:03:57.185897","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:04:27.371880","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:04:57.608658","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:05:27.845945","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:05:58.048107","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:06:28.321695","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:06:58.581784","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:07:28.810445","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:07:59.291125","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:08:29.567035","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:08:59.879654","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:09:30.280277","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:10:00.620874","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:10:31.104746","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:11:01.431195","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:11:31.672368","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:12:01.941339","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:12:32.356452","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:13:02.670478","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:13:33.021390","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:14:03.304423","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:14:33.670916","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:15:03.858037","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:15:34.118994","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:16:04.336425","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:16:34.591758","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:17:04.834950","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:17:35.153808","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:18:05.928498","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
