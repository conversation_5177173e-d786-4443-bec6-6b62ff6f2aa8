{"timestamp":"2025-05-30T10:52:45.190481","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:53:16.213463","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:53:46.605384","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:54:16.977492","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:54:47.343215","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:55:17.680069","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:55:47.882909","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:56:18.236318","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:56:48.481714","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:57:18.985199","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:57:49.368936","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:58:19.763914","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:58:50.227771","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:59:20.641068","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T10:59:51.193432","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:00:21.261570","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:00:51.407005","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:01:21.608534","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:01:51.777947","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:02:21.934047","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:02:52.249262","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:03:22.525159","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:03:52.707539","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:04:22.877879","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:04:53.076199","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:05:23.314939","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:05:53.545486","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:06:23.768021","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:06:54.016405","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:07:24.240086","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:07:54.690504","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:08:25.036711","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:08:55.292162","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:09:25.659720","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:09:55.971868","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:10:26.452242","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:10:56.912696","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:11:27.179180","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:11:57.405529","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:12:27.591066","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:12:58.123870","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:13:28.395415","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:13:58.775702","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:14:29.014167","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:14:59.300963","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:15:29.622046","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:15:59.814638","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:16:30.043119","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:17:00.330473","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:17:30.519527","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:18:00.889851","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-05-30T11:18:32.154269","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
